@echo off
chcp 65001 >nul
echo 🔍 اختبار OpenAI API
echo ========================

set /p API_KEY="أدخل مفتاح OpenAI API: "

if "%API_KEY%"=="" (
    echo ❌ يرجى إدخال مفتاح API
    pause
    exit /b 1
)

echo.
echo 🌐 جاري اختبار الاتصال...

curl -s "https://api.openai.com/v1/models" ^
  -H "Authorization: Bearer %API_KEY%" ^
  -H "OpenAI-Organization: org-P5cSc7mUQUglhzrolI2egjtH" > api_test_result.json

if %errorlevel% equ 0 (
    echo ✅ نجح الاتصال!
    echo.
    echo 📋 تم حفظ النتيجة في api_test_result.json
    echo يمكنك فتح الملف لرؤية النماذج المتاحة
) else (
    echo ❌ فشل الاتصال
    echo تحقق من:
    echo • صحة مفتاح API
    echo • اتصالك بالإنترنت
    echo • إعدادات Firewall
)

echo.
pause
