#!/usr/bin/env python3
"""
تطبيق بسيط لوصف الصور عبر سطر الأوامر
Simple command-line image describer
"""

import base64
import requests
import json
import sys
import os
from PIL import Image
import io

def image_to_base64(image_path):
    """تحويل الصورة إلى base64"""
    try:
        with Image.open(image_path) as image:
            # تصغير الصورة إذا كانت كبيرة لتوفير التكلفة
            if image.size[0] > 1024 or image.size[1] > 1024:
                image.thumbnail((1024, 1024), Image.Resampling.LANCZOS)
            
            buffer = io.BytesIO()
            # تحويل إلى RGB إذا كانت RGBA
            if image.mode == 'RGBA':
                image = image.convert('RGB')
            
            image.save(buffer, format='JPEG', quality=85)
            image_bytes = buffer.getvalue()
            return base64.b64encode(image_bytes).decode('utf-8')
    except Exception as e:
        print(f"❌ خطأ في تحميل الصورة: {e}")
        return None

def describe_image(api_key, image_base64, language="arabic"):
    """وصف الصورة باستخدام OpenAI GPT-4 Vision"""
    
    # نصوص الطلب حسب اللغة
    prompts = {
        "arabic": """قم بوصف هذه الصورة بشكل مفصل ودقيق جداً باللغة العربية. 
اذكر كل التفاصيل المرئية مثل:
- الأشخاص (العدد، الأعمار التقريبية، الملابس، التعبيرات)
- الأشياء والعناصر الموجودة
- الألوان والإضاءة
- المكان والخلفية
- الحالة المزاجية والجو العام
- أي نصوص مرئية
- التفاصيل الفنية إن وجدت

اجعل الوصف شاملاً ومفيداً.""",
        
        "english": """Describe this image in great detail and accuracy. 
Include all visible details such as:
- People (number, approximate ages, clothing, expressions)
- Objects and elements present
- Colors and lighting
- Location and background
- Mood and general atmosphere
- Any visible text
- Technical details if present

Make the description comprehensive and useful."""
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    payload = {
        "model": "gpt-4-vision-preview",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompts.get(language, prompts["arabic"])
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}"
                        }
                    }
                ]
            }
        ],
        "max_tokens": 1000
    }
    
    try:
        response = requests.post("https://api.openai.com/v1/chat/completions", 
                               headers=headers, json=payload, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            return f"❌ خطأ في API: {response.status_code} - {response.text}"
            
    except requests.exceptions.Timeout:
        return "❌ انتهت مهلة الاتصال. حاول مرة أخرى."
    except Exception as e:
        return f"❌ خطأ في الاتصال: {str(e)}"

def load_api_key():
    """تحميل مفتاح API من ملف أو متغير البيئة"""
    # محاولة تحميل من ملف
    if os.path.exists('api_key.txt'):
        try:
            with open('api_key.txt', 'r') as f:
                key = f.read().strip()
                if key:
                    return key
        except:
            pass
    
    # محاولة تحميل من متغير البيئة
    key = os.getenv('OPENAI_API_KEY')
    if key:
        return key
    
    return None

def save_api_key(api_key):
    """حفظ مفتاح API في ملف"""
    try:
        with open('api_key.txt', 'w') as f:
            f.write(api_key)
        print("✅ تم حفظ مفتاح API بنجاح!")
        return True
    except Exception as e:
        print(f"❌ فشل في حفظ مفتاح API: {e}")
        return False

def main():
    print("🖼️  تطبيق وصف الصور الذكي")
    print("=" * 40)
    
    # التحقق من وجود مسار الصورة
    if len(sys.argv) < 2:
        print("الاستخدام: python simple_describer.py <مسار_الصورة> [اللغة]")
        print("مثال: python simple_describer.py image.jpg arabic")
        print("اللغات المتاحة: arabic, english")
        return
    
    image_path = sys.argv[1]
    language = sys.argv[2] if len(sys.argv) > 2 else "arabic"
    
    # التحقق من وجود الصورة
    if not os.path.exists(image_path):
        print(f"❌ الصورة غير موجودة: {image_path}")
        return
    
    # تحميل مفتاح API
    api_key = load_api_key()
    
    if not api_key:
        print("🔑 لم يتم العثور على مفتاح OpenAI API")
        api_key = input("يرجى إدخال مفتاح OpenAI API: ").strip()
        
        if not api_key:
            print("❌ مفتاح API مطلوب للمتابعة")
            return
        
        # سؤال عن حفظ المفتاح
        save_choice = input("هل تريد حفظ المفتاح للاستخدام المستقبلي؟ (y/n): ").lower()
        if save_choice in ['y', 'yes', 'نعم', 'ن']:
            save_api_key(api_key)
    
    print(f"📸 تحليل الصورة: {image_path}")
    print("🔄 جاري التحليل... يرجى الانتظار")
    
    # تحويل الصورة إلى base64
    image_base64 = image_to_base64(image_path)
    if not image_base64:
        return
    
    # الحصول على الوصف
    description = describe_image(api_key, image_base64, language)
    
    print("\n" + "=" * 50)
    print("📝 وصف الصورة:")
    print("=" * 50)
    print(description)
    print("=" * 50)
    
    # حفظ النتيجة في ملف
    output_file = f"{os.path.splitext(image_path)[0]}_description.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"وصف الصورة: {image_path}\n")
            f.write("=" * 50 + "\n")
            f.write(description)
        print(f"💾 تم حفظ الوصف في: {output_file}")
    except Exception as e:
        print(f"⚠️  لم يتم حفظ الملف: {e}")

if __name__ == "__main__":
    main()
