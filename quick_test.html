<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 اختبار OpenAI API</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }

        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }

        input:focus {
            outline: none;
            border-color: #667eea;
        }

        button {
            width: 100%;
            padding: 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        button:hover {
            background: #5a6fd8;
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: monospace;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔍 اختبار OpenAI API</h1>

        <div class="input-group">
            <label for="apiKey">مفتاح OpenAI API:</label>
            <input type="password" id="apiKey" placeholder="sk-..."
                value="********************************************************************************************************************************************************************">
        </div>

        <div class="input-group">
            <label for="organization">Organization ID (اختياري):</label>
            <input type="text" id="organization" value="org-P5cSc7mUQUglhzrolI2egjtH">
        </div>

        <button onclick="testAPI()" id="testBtn">🚀 اختبار الاتصال</button>

        <div id="result"></div>
    </div>

    <script>
        async function testAPI() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const organization = document.getElementById('organization').value.trim();
            const resultDiv = document.getElementById('result');
            const testBtn = document.getElementById('testBtn');

            if (!apiKey) {
                showResult('❌ يرجى إدخال مفتاح API', 'error');
                return;
            }

            testBtn.disabled = true;
            testBtn.textContent = '🔄 جاري الاختبار...';
            showResult('🔄 جاري اختبار الاتصال مع OpenAI API...', 'loading');

            try {
                const headers = {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                };

                if (organization) {
                    headers['OpenAI-Organization'] = organization;
                }

                const response = await fetch('https://api.openai.com/v1/models', {
                    method: 'GET',
                    headers: headers
                });

                if (response.ok) {
                    const data = await response.json();

                    // البحث عن النماذج المهمة
                    const visionModels = data.data.filter(model =>
                        model.id.includes('vision') || model.id.includes('gpt-4')
                    );

                    const gpt4Vision = data.data.find(model => model.id === 'gpt-4-vision-preview');

                    let result = `✅ نجح الاتصال مع OpenAI API!\n\n`;
                    result += `📊 إجمالي النماذج المتاحة: ${data.data.length}\n\n`;

                    if (gpt4Vision) {
                        result += `🎯 نموذج GPT-4 Vision متاح! ✅\n`;
                        result += `   يمكنك استخدام تطبيق وصف الصور بنجاح.\n\n`;
                    } else {
                        result += `⚠️ نموذج GPT-4 Vision غير متاح في حسابك.\n\n`;
                    }

                    result += `🖼️ نماذج الرؤية المتاحة:\n`;
                    visionModels.slice(0, 5).forEach(model => {
                        result += `   • ${model.id}\n`;
                    });

                    if (visionModels.length > 5) {
                        result += `   ... و ${visionModels.length - 5} نماذج أخرى\n`;
                    }

                    showResult(result, 'success');

                } else {
                    const errorData = await response.json().catch(() => ({}));
                    let errorMsg = `❌ فشل الاتصال (${response.status})\n\n`;

                    switch (response.status) {
                        case 401:
                            errorMsg += `🔑 مفتاح API غير صحيح\n`;
                            errorMsg += `الحلول:\n`;
                            errorMsg += `• تحقق من صحة المفتاح\n`;
                            errorMsg += `• تأكد من أنه يبدأ بـ 'sk-'\n`;
                            errorMsg += `• تحقق من عدم وجود مسافات إضافية`;
                            break;
                        case 403:
                            errorMsg += `🚫 ليس لديك صلاحية للوصول\n`;
                            errorMsg += `الحلول:\n`;
                            errorMsg += `• تحقق من Organization ID\n`;
                            errorMsg += `• تأكد من أن حسابك مفعل`;
                            break;
                        case 429:
                            errorMsg += `⏰ تجاوز الحد المسموح\n`;
                            errorMsg += `الحلول:\n`;
                            errorMsg += `• انتظر قليلاً وحاول مرة أخرى\n`;
                            errorMsg += `• قلل من عدد الطلبات`;
                            break;
                        default:
                            errorMsg += `تفاصيل الخطأ: ${errorData.error?.message || 'خطأ غير معروف'}`;
                    }

                    showResult(errorMsg, 'error');
                }

            } catch (error) {
                showResult(`❌ خطأ في الاتصال:\n${error.message}\n\nتحقق من:\n• اتصالك بالإنترنت\n• إعدادات Firewall`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🚀 اختبار الاتصال';
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }

        // اختبار عند الضغط على Enter
        document.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                testAPI();
            }
        });
    </script>
</body>

</html>