<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖼️ وصف الصور الذكي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .api-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
        }

        .api-section h3 {
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .input-group input {
            flex: 1;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .upload-section {
            text-align: center;
        }

        .upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            padding: 40px 20px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .result-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            min-height: 400px;
        }

        .result-section h3 {
            color: #495057;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result-text {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            min-height: 300px;
            font-size: 16px;
            line-height: 1.6;
            white-space: pre-wrap;
            overflow-y: auto;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #667eea;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .input-group {
                flex-direction: column;
            }
        }

        .hidden {
            display: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ وصف الصور الذكي</h1>
            <p>احصل على وصف مفصل ودقيق لأي صورة باستخدام الذكاء الاصطناعي</p>
        </div>

        <div class="main-content">
            <!-- قسم إعداد API -->
            <div class="api-section">
                <h3>🔑 إعداد مفتاح OpenAI API</h3>
                <div class="input-group">
                    <input type="password" id="apiKey" placeholder="أدخل مفتاح OpenAI API هنا...">
                    <button class="btn btn-primary" onclick="saveApiKey()">حفظ المفتاح</button>
                </div>
                <div class="alert alert-info">
                    <strong>كيفية الحصول على مفتاح API:</strong><br>
                    1. اذهب إلى <a href="https://platform.openai.com" target="_blank">platform.openai.com</a><br>
                    2. سجل دخول أو أنشئ حساب جديد<br>
                    3. اذهب إلى "API Keys" وأنشئ مفتاح جديد<br>
                    4. انسخ المفتاح والصقه هنا
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="content-grid">
                <!-- قسم رفع الصورة -->
                <div class="upload-section">
                    <h3>📁 اختيار الصورة</h3>
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()" 
                         ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                        <div class="upload-icon">📷</div>
                        <h4>اضغط هنا أو اسحب الصورة</h4>
                        <p>يدعم: JPG, PNG, GIF, BMP</p>
                    </div>
                    <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
                    
                    <div id="imagePreview" class="hidden">
                        <img id="previewImg" class="image-preview" alt="معاينة الصورة">
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="describeImage()" id="describeBtn" disabled>
                            🔍 وصف الصورة
                        </button>
                        <button class="btn btn-secondary" onclick="clearImage()">
                            🗑️ مسح الصورة
                        </button>
                    </div>
                </div>

                <!-- قسم النتائج -->
                <div class="result-section">
                    <h3>📝 وصف الصورة</h3>
                    
                    <div id="loading" class="loading">
                        <div class="loading-spinner"></div>
                        <p>جاري تحليل الصورة... يرجى الانتظار</p>
                    </div>

                    <div id="resultText" class="result-text">
                        اختر صورة واضغط "وصف الصورة" للحصول على وصف مفصل...
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="copyResult()">
                            📋 نسخ النص
                        </button>
                        <button class="btn btn-danger" onclick="clearResult()">
                            🗑️ مسح النتيجة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
