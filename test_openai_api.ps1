# اختبار OpenAI API
# Test OpenAI API Connection

param(
    [Parameter(Mandatory=$true)]
    [string]$ApiKey,
    
    [Parameter(Mandatory=$false)]
    [string]$Organization = "org-P5cSc7mUQUglhzrolI2egjtH",
    
    [Parameter(Mandatory=$false)]
    [string]$ProjectId = ""
)

Write-Host "🔍 اختبار الاتصال مع OpenAI API..." -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

# إعداد Headers
$headers = @{
    "Authorization" = "Bearer $ApiKey"
    "Content-Type" = "application/json"
}

if ($Organization) {
    $headers["OpenAI-Organization"] = $Organization
    Write-Host "📋 Organization: $Organization" -ForegroundColor Yellow
}

if ($ProjectId) {
    $headers["OpenAI-Project"] = $ProjectId
    Write-Host "📁 Project ID: $ProjectId" -ForegroundColor Yellow
}

Write-Host ""

try {
    Write-Host "🌐 جاري الاتصال..." -ForegroundColor Blue
    
    # اختبار الاتصال مع API
    $response = Invoke-RestMethod -Uri "https://api.openai.com/v1/models" -Headers $headers -Method Get
    
    Write-Host "✅ نجح الاتصال!" -ForegroundColor Green
    Write-Host "=" * 50 -ForegroundColor Gray
    
    # عرض النماذج المتاحة
    Write-Host "📋 النماذج المتاحة:" -ForegroundColor Cyan
    
    $visionModels = $response.data | Where-Object { $_.id -like "*vision*" -or $_.id -like "*gpt-4*" }
    $textModels = $response.data | Where-Object { $_.id -like "*gpt-3.5*" -or $_.id -like "*gpt-4*" } | Select-Object -First 10
    
    Write-Host ""
    Write-Host "🖼️ نماذج الرؤية (للصور):" -ForegroundColor Magenta
    foreach ($model in $visionModels) {
        Write-Host "   • $($model.id)" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "💬 نماذج النص الرئيسية:" -ForegroundColor Green
    foreach ($model in $textModels) {
        Write-Host "   • $($model.id)" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "📊 إجمالي النماذج المتاحة: $($response.data.Count)" -ForegroundColor Yellow
    
    # اختبار نموذج GPT-4 Vision إذا كان متاحاً
    $gpt4Vision = $response.data | Where-Object { $_.id -eq "gpt-4-vision-preview" }
    if ($gpt4Vision) {
        Write-Host ""
        Write-Host "🎯 نموذج GPT-4 Vision متاح! يمكنك استخدام تطبيق وصف الصور." -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "⚠️ نموذج GPT-4 Vision غير متاح في حسابك حالياً." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ فشل الاتصال!" -ForegroundColor Red
    Write-Host "=" * 50 -ForegroundColor Gray
    
    $statusCode = $_.Exception.Response.StatusCode.value__
    $errorMessage = $_.Exception.Message
    
    Write-Host "🔍 تفاصيل الخطأ:" -ForegroundColor Yellow
    Write-Host "   كود الخطأ: $statusCode" -ForegroundColor White
    Write-Host "   الرسالة: $errorMessage" -ForegroundColor White
    
    # تحليل الأخطاء الشائعة
    switch ($statusCode) {
        401 { 
            Write-Host ""
            Write-Host "🔑 السبب المحتمل: مفتاح API غير صحيح" -ForegroundColor Red
            Write-Host "   الحلول:" -ForegroundColor Yellow
            Write-Host "   • تحقق من صحة مفتاح API" -ForegroundColor White
            Write-Host "   • تأكد من أن المفتاح يبدأ بـ 'sk-'" -ForegroundColor White
            Write-Host "   • تحقق من عدم وجود مسافات إضافية" -ForegroundColor White
        }
        403 { 
            Write-Host ""
            Write-Host "🚫 السبب المحتمل: ليس لديك صلاحية للوصول" -ForegroundColor Red
            Write-Host "   الحلول:" -ForegroundColor Yellow
            Write-Host "   • تحقق من Organization ID" -ForegroundColor White
            Write-Host "   • تأكد من أن حسابك مفعل" -ForegroundColor White
        }
        429 { 
            Write-Host ""
            Write-Host "⏰ السبب المحتمل: تجاوز الحد المسموح" -ForegroundColor Red
            Write-Host "   الحلول:" -ForegroundColor Yellow
            Write-Host "   • انتظر قليلاً وحاول مرة أخرى" -ForegroundColor White
            Write-Host "   • قلل من عدد الطلبات" -ForegroundColor White
        }
        default {
            Write-Host ""
            Write-Host "🌐 تحقق من:" -ForegroundColor Yellow
            Write-Host "   • اتصالك بالإنترنت" -ForegroundColor White
            Write-Host "   • إعدادات Firewall" -ForegroundColor White
        }
    }
}

Write-Host ""
Write-Host "=" * 50 -ForegroundColor Gray
Write-Host "🔗 روابط مفيدة:" -ForegroundColor Cyan
Write-Host "   • OpenAI Platform: https://platform.openai.com" -ForegroundColor Blue
Write-Host "   • API Keys: https://platform.openai.com/api-keys" -ForegroundColor Blue
Write-Host "   • Usage: https://platform.openai.com/usage" -ForegroundColor Blue
