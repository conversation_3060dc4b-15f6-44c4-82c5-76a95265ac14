// اختبار مفتاح OpenAI API الخاص بك
const API_KEY = "********************************************************************************************************************************************************************";

async function testAPI() {
    console.log("🔍 اختبار مفتاح OpenAI API...");
    
    try {
        // اختبار الاتصال مع API
        const response = await fetch('https://api.openai.com/v1/models', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log("✅ نجح الاتصال!");
            console.log(`📊 عدد النماذج المتاحة: ${data.data.length}`);
            
            // البحث عن نماذج مهمة
            const gpt4Models = data.data.filter(model => model.id.includes('gpt-4'));
            const visionModels = data.data.filter(model => model.id.includes('vision'));
            
            console.log("\n🤖 نماذج GPT-4 المتاحة:");
            gpt4Models.forEach(model => console.log(`   • ${model.id}`));
            
            if (visionModels.length > 0) {
                console.log("\n🖼️ نماذج الرؤية المتاحة:");
                visionModels.forEach(model => console.log(`   • ${model.id}`));
            }
            
            // اختبار نموذج GPT-4
            await testGPT4();
            
        } else {
            const errorData = await response.json();
            console.error("❌ فشل الاتصال:", response.status);
            console.error("تفاصيل الخطأ:", errorData.error?.message);
        }
        
    } catch (error) {
        console.error("❌ خطأ في الاتصال:", error.message);
    }
}

async function testGPT4() {
    console.log("\n🧪 اختبار نموذج GPT-4...");
    
    try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: "gpt-4",
                messages: [
                    {
                        role: "user",
                        content: "اكتب جملة واحدة عن الذكاء الاصطناعي باللغة العربية"
                    }
                ],
                max_tokens: 100
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log("✅ نجح اختبار GPT-4!");
            console.log("📝 الرد:", data.choices[0].message.content);
        } else {
            const errorData = await response.json();
            console.log("⚠️ فشل اختبار GPT-4:", errorData.error?.message);
        }
        
    } catch (error) {
        console.log("❌ خطأ في اختبار GPT-4:", error.message);
    }
}

// تشغيل الاختبار
testAPI();
