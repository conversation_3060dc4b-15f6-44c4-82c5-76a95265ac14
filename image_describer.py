import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
from PIL import Image, ImageTk
import base64
import io
import requests
import json
import os
from pathlib import Path

class ImageDescriberApp:
    def __init__(self, root):
        self.root = root
        self.root.title("وصف الصور الذكي - Image Describer")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات التطبيق
        self.current_image = None
        self.api_key = ""
        
        self.setup_ui()
        self.load_api_key()
    
    def setup_ui(self):
        # إعداد الواجهة الرئيسية
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان التطبيق
        title_label = tk.Label(main_frame, text="🖼️ وصف الصور الذكي", 
                              font=('Arial', 20, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # إعداد مفتاح API
        api_frame = tk.Frame(main_frame, bg='#f0f0f0')
        api_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(api_frame, text="مفتاح OpenAI API:", font=('Arial', 12), 
                bg='#f0f0f0').pack(side=tk.LEFT)
        
        self.api_entry = tk.Entry(api_frame, font=('Arial', 10), width=50, show="*")
        self.api_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        save_api_btn = tk.Button(api_frame, text="حفظ", command=self.save_api_key,
                                bg='#3498db', fg='white', font=('Arial', 10))
        save_api_btn.pack(side=tk.LEFT, padx=5)
        
        # إطار الصورة والأزرار
        image_frame = tk.Frame(main_frame, bg='#f0f0f0')
        image_frame.pack(fill=tk.BOTH, expand=True)
        
        # الجانب الأيسر - الصورة
        left_frame = tk.Frame(image_frame, bg='#f0f0f0')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # منطقة عرض الصورة
        self.image_label = tk.Label(left_frame, text="اختر صورة لعرضها هنا", 
                                   bg='white', relief=tk.SUNKEN, bd=2,
                                   font=('Arial', 14), fg='#7f8c8d')
        self.image_label.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(left_frame, bg='#f0f0f0')
        buttons_frame.pack(fill=tk.X)
        
        select_btn = tk.Button(buttons_frame, text="📁 اختيار صورة", 
                              command=self.select_image, bg='#27ae60', fg='white',
                              font=('Arial', 12, 'bold'), pady=8)
        select_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        describe_btn = tk.Button(buttons_frame, text="🔍 وصف الصورة", 
                                command=self.describe_image, bg='#e74c3c', fg='white',
                                font=('Arial', 12, 'bold'), pady=8)
        describe_btn.pack(side=tk.LEFT)
        
        # الجانب الأيمن - النتائج
        right_frame = tk.Frame(image_frame, bg='#f0f0f0')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # عنوان منطقة النتائج
        result_title = tk.Label(right_frame, text="📝 وصف الصورة", 
                               font=('Arial', 16, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        result_title.pack(pady=(0, 10))
        
        # منطقة النص
        self.result_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, 
                                                    font=('Arial', 11), height=20)
        self.result_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # أزرار إضافية
        extra_buttons_frame = tk.Frame(right_frame, bg='#f0f0f0')
        extra_buttons_frame.pack(fill=tk.X)
        
        copy_btn = tk.Button(extra_buttons_frame, text="📋 نسخ النص", 
                            command=self.copy_text, bg='#9b59b6', fg='white',
                            font=('Arial', 10))
        copy_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        clear_btn = tk.Button(extra_buttons_frame, text="🗑️ مسح", 
                             command=self.clear_text, bg='#95a5a6', fg='white',
                             font=('Arial', 10))
        clear_btn.pack(side=tk.LEFT)
    
    def load_api_key(self):
        """تحميل مفتاح API المحفوظ"""
        try:
            if os.path.exists('api_key.txt'):
                with open('api_key.txt', 'r') as f:
                    self.api_key = f.read().strip()
                    self.api_entry.insert(0, self.api_key)
        except:
            pass
    
    def save_api_key(self):
        """حفظ مفتاح API"""
        self.api_key = self.api_entry.get().strip()
        if self.api_key:
            try:
                with open('api_key.txt', 'w') as f:
                    f.write(self.api_key)
                messagebox.showinfo("نجح", "تم حفظ مفتاح API بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ مفتاح API: {str(e)}")
        else:
            messagebox.showwarning("تحذير", "يرجى إدخال مفتاح API صحيح")
    
    def select_image(self):
        """اختيار صورة من الجهاز"""
        file_types = [
            ("جميع الصور", "*.jpg *.jpeg *.png *.gif *.bmp *.tiff"),
            ("JPEG", "*.jpg *.jpeg"),
            ("PNG", "*.png"),
            ("جميع الملفات", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="اختر صورة",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # تحميل وعرض الصورة
                image = Image.open(file_path)
                self.current_image = image.copy()
                
                # تغيير حجم الصورة للعرض
                display_image = self.resize_image_for_display(image)
                photo = ImageTk.PhotoImage(display_image)
                
                self.image_label.configure(image=photo, text="")
                self.image_label.image = photo  # حفظ مرجع للصورة
                
                # مسح النص السابق
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, "تم تحميل الصورة بنجاح! اضغط 'وصف الصورة' للبدء.")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل الصورة: {str(e)}")
    
    def resize_image_for_display(self, image, max_size=(400, 400)):
        """تغيير حجم الصورة للعرض"""
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
        return image
    
    def describe_image(self):
        """وصف الصورة باستخدام OpenAI"""
        if not self.current_image:
            messagebox.showwarning("تحذير", "يرجى اختيار صورة أولاً")
            return
        
        if not self.api_key:
            messagebox.showwarning("تحذير", "يرجى إدخال مفتاح OpenAI API")
            return
        
        try:
            # عرض رسالة التحميل
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "🔄 جاري تحليل الصورة... يرجى الانتظار")
            self.root.update()
            
            # تحويل الصورة إلى base64
            image_base64 = self.image_to_base64(self.current_image)
            
            # إرسال الطلب إلى OpenAI
            description = self.get_image_description(image_base64)
            
            # عرض النتيجة
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, description)
            
        except Exception as e:
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"❌ حدث خطأ: {str(e)}")
    
    def image_to_base64(self, image):
        """تحويل الصورة إلى base64"""
        # تغيير حجم الصورة لتوفير التكلفة
        if image.size[0] > 1024 or image.size[1] > 1024:
            image.thumbnail((1024, 1024), Image.Resampling.LANCZOS)
        
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG', quality=85)
        image_bytes = buffer.getvalue()
        return base64.b64encode(image_bytes).decode('utf-8')
    
    def get_image_description(self, image_base64):
        """الحصول على وصف الصورة من OpenAI"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        payload = {
            "model": "gpt-4-vision-preview",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """قم بوصف هذه الصورة بشكل مفصل ودقيق جداً باللغة العربية. 
                            اذكر كل التفاصيل المرئية مثل:
                            - الأشخاص (العدد، الأعمار التقريبية، الملابس، التعبيرات)
                            - الأشياء والعناصر الموجودة
                            - الألوان والإضاءة
                            - المكان والخلفية
                            - الحالة المزاجية والجو العام
                            - أي نصوص مرئية
                            - التفاصيل الفنية إن وجدت
                            
                            اجعل الوصف شاملاً ومفيداً."""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000
        }
        
        response = requests.post("https://api.openai.com/v1/chat/completions", 
                               headers=headers, json=payload)
        
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            raise Exception(f"خطأ في API: {response.status_code} - {response.text}")
    
    def copy_text(self):
        """نسخ النص إلى الحافظة"""
        text = self.result_text.get(1.0, tk.END).strip()
        if text:
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("نجح", "تم نسخ النص إلى الحافظة!")
    
    def clear_text(self):
        """مسح النص"""
        self.result_text.delete(1.0, tk.END)

if __name__ == "__main__":
    root = tk.Tk()
    app = ImageDescriberApp(root)
    root.mainloop()
