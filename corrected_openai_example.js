// مثال صحيح لاستخدام OpenAI API
import OpenAI from "openai";

// إعداد العميل مع مفتاحك
const client = new OpenAI({
    apiKey: "********************************************************************************************************************************************************************"
});

// المثال الأصلي مصحح
async function bedtimeStoryExample() {
    try {
        const response = await client.chat.completions.create({
            model: "gpt-4", // الاسم الصحيح للنموذج
            messages: [
                {
                    role: "user", 
                    content: "Write a one-sentence bedtime story about a unicorn."
                }
            ],
            max_tokens: 100,
            temperature: 0.7
        });

        console.log("🦄 قصة ما قبل النوم:");
        console.log(response.choices[0].message.content);
        
    } catch (error) {
        console.error("❌ خطأ:", error.message);
    }
}

// مثال باللغة العربية
async function arabicExample() {
    try {
        const response = await client.chat.completions.create({
            model: "gpt-4",
            messages: [
                {
                    role: "user", 
                    content: "اكتب قصة قصيرة عن وحيد القرن باللغة العربية"
                }
            ],
            max_tokens: 200,
            temperature: 0.8
        });

        console.log("📖 قصة باللغة العربية:");
        console.log(response.choices[0].message.content);
        
    } catch (error) {
        console.error("❌ خطأ:", error.message);
    }
}

// مثال لوصف الصور (إذا كان متاحاً)
async function imageDescriptionExample() {
    try {
        // تحقق من توفر نموذج الرؤية
        const response = await client.chat.completions.create({
            model: "gpt-4-vision-preview",
            messages: [
                {
                    role: "user",
                    content: [
                        {
                            type: "text",
                            text: "صف هذه الصورة بالتفصيل باللغة العربية"
                        },
                        {
                            type: "image_url",
                            image_url: {
                                url: "https://example.com/image.jpg", // ضع رابط الصورة هنا
                                detail: "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens: 500
        });

        console.log("🖼️ وصف الصورة:");
        console.log(response.choices[0].message.content);
        
    } catch (error) {
        console.error("❌ خطأ في وصف الصورة:", error.message);
    }
}

// اختبار جميع الأمثلة
async function runAllExamples() {
    console.log("🚀 بدء اختبار OpenAI API...\n");
    
    // المثال الأصلي
    await bedtimeStoryExample();
    console.log("\n" + "=".repeat(50) + "\n");
    
    // مثال عربي
    await arabicExample();
    console.log("\n" + "=".repeat(50) + "\n");
    
    // مثال وصف الصور (اختياري)
    console.log("💡 لاختبار وصف الصور، استخدم تطبيق الويب الذي أنشأناه");
}

// تشغيل الأمثلة
runAllExamples();

// تصدير الدوال للاستخدام في ملفات أخرى
export { 
    client, 
    bedtimeStoryExample, 
    arabicExample, 
    imageDescriptionExample 
};
