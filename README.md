# 🖼️ تطبيق وصف الصور الذكي

تطبيق Python مع واجهة رسومية لوصف الصور بدقة عالية باستخدام الذكاء الاصطناعي.

## ✨ المميزات

- 🎯 وصف دقيق ومفصل للصور باللغة العربية
- 🖥️ واجهة رسومية سهلة الاستخدام
- 🔒 حفظ آمن لمفتاح API
- 📋 نسخ النتائج بسهولة
- 🖼️ دعم جميع صيغ الصور الشائعة

## 🚀 التثبيت والتشغيل

### 1. تثبيت Python
تأكد من تثبيت Python 3.8 أو أحدث على جهازك.

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

### 3. الحصول على مفتاح OpenAI API
1. اذهب إلى [OpenAI Platform](https://platform.openai.com/)
2. سجل حساب جديد أو سجل دخول
3. اذهب إلى API Keys
4. أنشئ مفتاح API جديد
5. انسخ المفتاح (احتفظ به آمناً!)

### 4. تشغيل التطبيق
```bash
python image_describer.py
```

## 📖 طريقة الاستخدام

1. **إدخال مفتاح API**: 
   - الصق مفتاح OpenAI API في الحقل المخصص
   - اضغط "حفظ" (سيتم حفظه للاستخدام المستقبلي)

2. **اختيار صورة**:
   - اضغط "📁 اختيار صورة"
   - اختر الصورة من جهازك

3. **الحصول على الوصف**:
   - اضغط "🔍 وصف الصورة"
   - انتظر قليلاً للحصول على الوصف المفصل

4. **استخدام النتائج**:
   - اقرأ الوصف المفصل
   - انسخ النص باستخدام "📋 نسخ النص"
   - امسح النتائج باستخدام "🗑️ مسح"

## 💡 نصائح للحصول على أفضل النتائج

- استخدم صور واضحة وعالية الجودة
- تأكد من أن الصورة ليست كبيرة جداً (سيتم تصغيرها تلقائياً)
- الصور ذات التفاصيل الواضحة تعطي وصف أفضل

## 🔧 استكشاف الأخطاء

### خطأ في مفتاح API
- تأكد من صحة مفتاح API
- تحقق من وجود رصيد في حسابك على OpenAI

### خطأ في تحميل الصورة
- تأكد من أن الملف صورة صحيحة
- جرب صيغة صورة مختلفة (JPG, PNG)

### بطء في الاستجابة
- هذا طبيعي، تحليل الصور يحتاج وقت
- تأكد من اتصالك بالإنترنت

## 📁 ملفات المشروع

- `image_describer.py` - الملف الرئيسي للتطبيق
- `requirements.txt` - المكتبات المطلوبة
- `api_key.txt` - ملف حفظ مفتاح API (يتم إنشاؤه تلقائياً)

## 🛡️ الأمان

- مفتاح API يتم حفظه محلياً على جهازك فقط
- لا يتم إرسال أي بيانات لخوادم أخرى غير OpenAI
- يمكنك حذف ملف `api_key.txt` في أي وقت

## 🆘 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المكتبات المطلوبة
2. تحقق من صحة مفتاح API
3. تأكد من اتصالك بالإنترنت

---

**ملاحظة**: هذا التطبيق يستخدم OpenAI GPT-4 Vision، وقد تنطبق رسوم حسب الاستخدام.
