// متغيرات عامة
let currentImage = null;
let apiKey = '';

// تحميل البيانات المحفوظة عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    loadApiKey();
    showAlert('مرحباً! أدخل مفتاح OpenAI API واختر صورة للبدء', 'info');
});

// حفظ وتحميل مفتاح API
function saveApiKey() {
    const keyInput = document.getElementById('apiKey');
    apiKey = keyInput.value.trim();
    
    if (!apiKey) {
        showAlert('يرجى إدخال مفتاح API صحيح', 'error');
        return;
    }
    
    // حفظ في localStorage
    localStorage.setItem('openai_api_key', apiKey);
    showAlert('تم حفظ مفتاح API بنجاح! ✅', 'success');
    
    // تحديث حالة الأزرار
    updateButtonStates();
}

function loadApiKey() {
    const savedKey = localStorage.getItem('openai_api_key');
    if (savedKey) {
        apiKey = savedKey;
        document.getElementById('apiKey').value = savedKey;
        updateButtonStates();
    }
}

// التعامل مع الملفات
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processImageFile(file);
    }
}

function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const uploadArea = event.currentTarget;
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            processImageFile(file);
        } else {
            showAlert('يرجى اختيار ملف صورة صحيح', 'error');
        }
    }
}

function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('dragover');
}

function processImageFile(file) {
    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        showAlert('يرجى اختيار ملف صورة صحيح', 'error');
        return;
    }
    
    // التحقق من حجم الملف (أقل من 20MB)
    if (file.size > 20 * 1024 * 1024) {
        showAlert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 20MB', 'error');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        currentImage = e.target.result;
        displayImagePreview(currentImage);
        updateButtonStates();
        showAlert('تم تحميل الصورة بنجاح! 📸', 'success');
    };
    reader.readAsDataURL(file);
}

function displayImagePreview(imageSrc) {
    const previewDiv = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    
    previewImg.src = imageSrc;
    previewDiv.classList.remove('hidden');
    previewDiv.classList.add('fade-in');
}

function clearImage() {
    currentImage = null;
    document.getElementById('fileInput').value = '';
    document.getElementById('imagePreview').classList.add('hidden');
    updateButtonStates();
    clearResult();
    showAlert('تم مسح الصورة', 'info');
}

// وصف الصورة
async function describeImage() {
    if (!currentImage) {
        showAlert('يرجى اختيار صورة أولاً', 'error');
        return;
    }
    
    if (!apiKey) {
        showAlert('يرجى إدخال مفتاح OpenAI API أولاً', 'error');
        return;
    }
    
    // إظهار التحميل
    showLoading(true);
    clearResult();
    
    try {
        // تحضير البيانات
        const base64Image = currentImage.split(',')[1]; // إزالة data:image/jpeg;base64,
        
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: "gpt-4-vision-preview",
                messages: [
                    {
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `قم بوصف هذه الصورة بشكل مفصل ودقيق جداً باللغة العربية. 
اذكر كل التفاصيل المرئية مثل:
- الأشخاص (العدد، الأعمار التقريبية، الملابس، التعبيرات)
- الأشياء والعناصر الموجودة
- الألوان والإضاءة
- المكان والخلفية
- الحالة المزاجية والجو العام
- أي نصوص مرئية
- التفاصيل الفنية إن وجدت

اجعل الوصف شاملاً ومفيداً وباللغة العربية الفصحى.`
                            },
                            {
                                type: "image_url",
                                image_url: {
                                    url: currentImage,
                                    detail: "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens: 1000,
                temperature: 0.7
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`خطأ في API: ${response.status} - ${errorData.error?.message || 'خطأ غير معروف'}`);
        }
        
        const data = await response.json();
        const description = data.choices[0].message.content;
        
        // عرض النتيجة
        showResult(description);
        showAlert('تم تحليل الصورة بنجاح! ✨', 'success');
        
    } catch (error) {
        console.error('Error:', error);
        let errorMessage = 'حدث خطأ أثناء تحليل الصورة';
        
        if (error.message.includes('401')) {
            errorMessage = 'مفتاح API غير صحيح. يرجى التحقق من المفتاح';
        } else if (error.message.includes('429')) {
            errorMessage = 'تم تجاوز الحد المسموح. يرجى المحاولة لاحقاً';
        } else if (error.message.includes('insufficient_quota')) {
            errorMessage = 'رصيد API منتهي. يرجى إضافة رصيد لحسابك';
        } else if (error.message.includes('network')) {
            errorMessage = 'خطأ في الاتصال. تحقق من الإنترنت';
        }
        
        showResult(`❌ ${errorMessage}\n\nتفاصيل الخطأ: ${error.message}`);
        showAlert(errorMessage, 'error');
    } finally {
        showLoading(false);
    }
}

// عرض النتائج
function showResult(text) {
    const resultDiv = document.getElementById('resultText');
    resultDiv.textContent = text;
    resultDiv.classList.add('fade-in');
}

function clearResult() {
    document.getElementById('resultText').textContent = 'اختر صورة واضغط "وصف الصورة" للحصول على وصف مفصل...';
}

function copyResult() {
    const resultText = document.getElementById('resultText').textContent;
    
    if (resultText && resultText !== 'اختر صورة واضغط "وصف الصورة" للحصول على وصف مفصل...') {
        navigator.clipboard.writeText(resultText).then(() => {
            showAlert('تم نسخ النص بنجاح! 📋', 'success');
        }).catch(() => {
            // fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = resultText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showAlert('تم نسخ النص بنجاح! 📋', 'success');
        });
    } else {
        showAlert('لا يوجد نص لنسخه', 'error');
    }
}

// وظائف مساعدة
function showLoading(show) {
    const loading = document.getElementById('loading');
    if (show) {
        loading.style.display = 'block';
    } else {
        loading.style.display = 'none';
    }
}

function updateButtonStates() {
    const describeBtn = document.getElementById('describeBtn');
    const hasImage = currentImage !== null;
    const hasApiKey = apiKey !== '';
    
    describeBtn.disabled = !(hasImage && hasApiKey);
    
    if (hasImage && hasApiKey) {
        describeBtn.style.opacity = '1';
        describeBtn.style.cursor = 'pointer';
    } else {
        describeBtn.style.opacity = '0.6';
        describeBtn.style.cursor = 'not-allowed';
    }
}

function showAlert(message, type) {
    // إزالة التنبيهات السابقة
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => {
        if (alert.classList.contains('temp-alert')) {
            alert.remove();
        }
    });
    
    // إنشاء تنبيه جديد
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} temp-alert fade-in`;
    alert.textContent = message;
    
    // إضافة التنبيه
    const mainContent = document.querySelector('.main-content');
    mainContent.insertBefore(alert, mainContent.firstChild);
    
    // إزالة التنبيه بعد 5 ثواني
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.opacity = '0';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 300);
        }
    }, 5000);
}

// تحسينات إضافية
document.addEventListener('paste', function(e) {
    const items = e.clipboardData.items;
    for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
            const blob = items[i].getAsFile();
            processImageFile(blob);
            showAlert('تم لصق الصورة من الحافظة! 📋', 'success');
            break;
        }
    }
});

// منع السحب والإفلات على الصفحة كاملة
document.addEventListener('dragover', function(e) {
    e.preventDefault();
});

document.addEventListener('drop', function(e) {
    e.preventDefault();
});
